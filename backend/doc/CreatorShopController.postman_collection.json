{"info": {"_postman_id": "creator-shop-controller-api", "name": "CreatorShopController API 接口文档", "description": "# 创作者店铺管理API接口文档\n\n## 功能概述\n创作者店铺管理相关接口，包括店铺创建、查询、进入、离开等功能。\n\n## 接口列表\n1. **POST** `/creator_shop/createShop` - 创建店铺\n2. **GET** `/creator_shop/queryMyShopList` - 查询我的店铺列表\n3. **POST** `/creator_shop/enterShopGetShopToken` - 进入店铺获取店铺Token\n4. **GET** `/creator_shop/inShop/queryShopInfo` - 店铺内查询店铺信息\n5. **GET** `/creator_shop/leaveShop/changeShopTokenToCreatorToken` - 离开店铺换取创作者Token\n\n## 认证说明\n- 所有接口都使用 `@Anonymous` 注解，但实际需要JWT令牌验证\n- 创作者相关操作使用 `creatorToken`\n- 店铺内操作使用 `shopToken`\n\n## 数据字典\n\n### WendaoShop 店铺对象字段说明\n- `id`: 自增主键\n- `creatorId`: 店主ID（创作者ID）\n- `shopId`: 店铺唯一ID（前缀为app）\n- `appId`: 应用ID（与shopId相同）\n- `shopName`: 店铺名称\n- `shopLogo`: 店铺LOGO地址\n- `createdAt`: 店铺创建时间\n- `expireTime`: 服务到期日期\n- `versionType`: 版本类型（0-试用版,1-标准版,2-专业版,3-旗舰版）\n- `rightsType`: 权益类型（-1=无权益）\n- `isSealed`: 是否被封禁（0=未封禁,1=封禁）\n- `useCollection`: 是否使用收藏功能（0=否,1=是）\n- `hasExpired`: 是否已过期（0=未过期,1=过期）\n- `hasActivateOrder`: 是否有未支付激活订单（0=否,1=是）\n- `status`: 店铺状态（0=正常,1=已关闭）\n- `lastLogin`: 是否最后登录店铺（0=否,1=是）\n- `isWaitSeal`: 是否等待封禁中（0=否,1=是）\n- `isDrop`: 是否已注销（0=未注销,1=已注销）\n- `entryMode`: 进入模式\n- `isTry`: 是否试用店铺（0=不是,1=是）\n- `isShowVersion`: 是否显示版本信息（0=否,1=是）\n- `showRenewal`: 是否显示续费按钮（0=否,1=是）\n- `showExpireTime`: 是否显示到期时间（0=否,1=是）\n\n### CreatorInfo 创作者对象字段说明\n- `id`: 主键ID\n- `creatorId`: 创作者ID（UUID去除-后的值）\n- `avatarUrl`: 头像地址\n- `openid`: 微信OpenID\n- `unionid`: 微信UnionID\n- `wxAccount`: 微信账号\n- `phoneNumber`: 手机号（含国家区号）\n- `purePhoneNumber`: 纯手机号（不含区号）\n- `countryCode`: 国家区号\n- `wxMaAppid`: 微信小程序APPID\n- `wxNickName`: 微信昵称\n- `wxHeadimgurl`: 微信头像URL\n\n## 业务规则\n1. 每个创作者最多只能创建3个店铺\n2. 新创建的店铺默认为试用版，有效期7天\n3. 店铺ID自动生成，格式为 `app` + 12位随机字符\n4. 进入店铺时需要用creatorToken换取shopToken\n5. 在店铺内操作必须使用shopToken\n6. 离开店铺时可以将shopToken换回creatorToken", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "创建店铺", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{<PERSON>T<PERSON>}}", "type": "text", "description": "创作者JWT令牌，用于身份验证"}], "body": {"mode": "raw", "raw": "{\n    \"shopName\": \"我的测试店铺\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/creator_shop/createShop", "host": ["{{baseUrl}}"], "path": ["creator_shop", "createShop"]}, "description": "创建新的店铺。每个创作者最多只能创建3个店铺。创建的店铺默认为试用版，有效期7天。\n\n**请求参数说明：**\n- shopName: 店铺名称（必填，最大100个字符）\n\n**业务逻辑：**\n1. 验证创作者身份\n2. 检查店铺数量限制（最多3个）\n3. 生成唯一店铺ID（前缀为app）\n4. 设置试用版默认配置\n5. 设置7天试用期\n\n**返回数据包含完整的店铺信息对象**"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"shopName\": \"我的测试店铺\"\n}"}, "url": {"raw": "{{baseUrl}}/creator_shop/createShop", "host": ["{{baseUrl}}"], "path": ["creator_shop", "createShop"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"店铺创建成功\",\n    \"data\": {\n        \"id\": 1,\n        \"creatorId\": \"creator123456\",\n        \"shopId\": \"app1a2b3c4d5e6f\",\n        \"appId\": \"app1a2b3c4d5e6f\",\n        \"shopName\": \"我的测试店铺\",\n        \"shopLogo\": \"\",\n        \"createdAt\": \"2025-01-15 10:30:00\",\n        \"expireTime\": \"2025-01-22\",\n        \"versionType\": 0,\n        \"rightsType\": -1,\n        \"isSealed\": 0,\n        \"useCollection\": 0,\n        \"hasExpired\": 0,\n        \"hasActivateOrder\": 0,\n        \"status\": 0,\n        \"lastLogin\": 1,\n        \"isWaitSeal\": 0,\n        \"isDrop\": 0,\n        \"entryMode\": 0,\n        \"isTry\": 1,\n        \"isShowVersion\": 1,\n        \"showRenewal\": 1,\n        \"showExpireTime\": 1\n    }\n}"}, {"name": "店铺数量超限", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"shopName\": \"第四个店铺\"\n}"}, "url": {"raw": "{{baseUrl}}/creator_shop/createShop", "host": ["{{baseUrl}}"], "path": ["creator_shop", "createShop"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"code\": 500,\n    \"msg\": \"您创建的店铺数量已达到上限\"\n}"}]}, {"name": "查询我的店铺列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON>T<PERSON>}}", "type": "text", "description": "创作者JWT令牌"}], "url": {"raw": "{{baseUrl}}/creator_shop/queryMyShopList", "host": ["{{baseUrl}}"], "path": ["creator_shop", "queryMyShopList"]}, "description": "查询当前创作者名下的所有店铺列表。\n\n**功能说明：**\n- 根据创作者ID查询所有店铺\n- 返回店铺的完整信息\n- 包括店铺状态、版本类型、到期时间等\n\n**无需请求参数，通过JWT令牌获取创作者身份**"}, "response": [{"name": "查询成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/creator_shop/queryMyShopList", "host": ["{{baseUrl}}"], "path": ["creator_shop", "queryMyShopList"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"操作成功\",\n    \"data\": [\n        {\n            \"id\": 1,\n            \"creatorId\": \"creator123456\",\n            \"shopId\": \"app1a2b3c4d5e6f\",\n            \"appId\": \"app1a2b3c4d5e6f\",\n            \"shopName\": \"我的第一个店铺\",\n            \"shopLogo\": \"\",\n            \"createdAt\": \"2025-01-15 10:30:00\",\n            \"expireTime\": \"2025-01-22\",\n            \"versionType\": 0,\n            \"rightsType\": -1,\n            \"isSealed\": 0,\n            \"useCollection\": 0,\n            \"hasExpired\": 0,\n            \"hasActivateOrder\": 0,\n            \"status\": 0,\n            \"lastLogin\": 1,\n            \"isWaitSeal\": 0,\n            \"isDrop\": 0,\n            \"entryMode\": 0,\n            \"isTry\": 1,\n            \"isShowVersion\": 1,\n            \"showRenewal\": 1,\n            \"showExpireTime\": 1\n        },\n        {\n            \"id\": 2,\n            \"creatorId\": \"creator123456\",\n            \"shopId\": \"app2b3c4d5e6f7g\",\n            \"appId\": \"app2b3c4d5e6f7g\",\n            \"shopName\": \"我的第二个店铺\",\n            \"shopLogo\": \"\",\n            \"createdAt\": \"2025-01-14 15:20:00\",\n            \"expireTime\": \"2025-01-21\",\n            \"versionType\": 0,\n            \"rightsType\": -1,\n            \"isSealed\": 0,\n            \"useCollection\": 0,\n            \"hasExpired\": 0,\n            \"hasActivateOrder\": 0,\n            \"status\": 0,\n            \"lastLogin\": 0,\n            \"isWaitSeal\": 0,\n            \"isDrop\": 0,\n            \"entryMode\": 0,\n            \"isTry\": 1,\n            \"isShowVersion\": 1,\n            \"showRenewal\": 1,\n            \"showExpireTime\": 1\n        }\n    ]\n}"}]}, {"name": "进入店铺获取店铺Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{<PERSON>T<PERSON>}}", "type": "text", "description": "创作者JWT令牌"}], "body": {"mode": "raw", "raw": "{\n    \"shopId\": \"app1a2b3c4d5e6f\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/creator_shop/enterShopGetShopToken", "host": ["{{baseUrl}}"], "path": ["creator_shop", "enterShopGetShopToken"]}, "description": "创作者进入指定店铺，用创作者Token换取店铺Token。\n\n**请求参数说明：**\n- shopId: 要进入的店铺ID（必填）\n\n**功能说明：**\n1. 验证创作者身份和店铺归属\n2. 生成店铺专用的JWT令牌\n3. 返回店铺信息和店铺Token\n4. 前端应保存shopToken用于后续店铺内操作\n\n**返回数据包含店铺信息和shopToken**"}, "response": [{"name": "进入成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"shopId\": \"app1a2b3c4d5e6f\"\n}"}, "url": {"raw": "{{baseUrl}}/creator_shop/enterShopGetShopToken", "host": ["{{baseUrl}}"], "path": ["creator_shop", "enterShopGetShopToken"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"操作成功\",\n    \"data\": {\n        \"wendaoShop\": {\n            \"id\": 1,\n            \"creatorId\": \"creator123456\",\n            \"shopId\": \"app1a2b3c4d5e6f\",\n            \"appId\": \"app1a2b3c4d5e6f\",\n            \"shopName\": \"我的测试店铺\",\n            \"shopLogo\": \"\",\n            \"createdAt\": \"2025-01-15 10:30:00\",\n            \"expireTime\": \"2025-01-22\",\n            \"versionType\": 0,\n            \"rightsType\": -1,\n            \"isSealed\": 0,\n            \"useCollection\": 0,\n            \"hasExpired\": 0,\n            \"hasActivateOrder\": 0,\n            \"status\": 0,\n            \"lastLogin\": 1,\n            \"isWaitSeal\": 0,\n            \"isDrop\": 0,\n            \"entryMode\": 0,\n            \"isTry\": 1,\n            \"isShowVersion\": 1,\n            \"showRenewal\": 1,\n            \"showExpireTime\": 1\n        },\n        \"shopToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"\n    }\n}"}, {"name": "店铺ID为空", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"shopId\": \"\"\n}"}, "url": {"raw": "{{baseUrl}}/creator_shop/enterShopGetShopToken", "host": ["{{baseUrl}}"], "path": ["creator_shop", "enterShopGetShopToken"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"code\": 500,\n    \"msg\": \"店铺ID不能为空\"\n}"}, {"name": "店铺不存在", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"shopId\": \"app999999999999\"\n}"}, "url": {"raw": "{{baseUrl}}/creator_shop/enterShopGetShopToken", "host": ["{{baseUrl}}"], "path": ["creator_shop", "enterShopGetShopToken"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"code\": 500,\n    \"msg\": \"店铺不存在\"\n}"}]}, {"name": "店铺内查询店铺信息", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{shopToken}}", "type": "text", "description": "店铺JWT令牌（进入店铺后使用）"}], "url": {"raw": "{{baseUrl}}/creator_shop/inShop/queryShopInfo", "host": ["{{baseUrl}}"], "path": ["creator_shop", "inShop", "queryShopInfo"]}, "description": "在店铺内部查询当前店铺的详细信息。此接口需要使用shopToken。\n\n**使用场景：**\n- 进入店铺后获取店铺详细信息\n- 刷新店铺状态信息\n- 检查店铺配置和权限\n\n**注意事项：**\n- 必须先调用进入店铺接口获取shopToken\n- 使用shopToken而不是creatorToken\n- 返回当前店铺的完整信息"}, "response": [{"name": "查询成功", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{shopToken}}"}], "url": {"raw": "{{baseUrl}}/creator_shop/inShop/queryShopInfo", "host": ["{{baseUrl}}"], "path": ["creator_shop", "inShop", "queryShopInfo"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"操作成功\",\n    \"data\": {\n        \"id\": 1,\n        \"creatorId\": \"creator123456\",\n        \"shopId\": \"app1a2b3c4d5e6f\",\n        \"appId\": \"app1a2b3c4d5e6f\",\n        \"shopName\": \"我的测试店铺\",\n        \"shopLogo\": \"\",\n        \"createdAt\": \"2025-01-15 10:30:00\",\n        \"expireTime\": \"2025-01-22\",\n        \"versionType\": 0,\n        \"rightsType\": -1,\n        \"isSealed\": 0,\n        \"useCollection\": 0,\n        \"hasExpired\": 0,\n        \"hasActivateOrder\": 0,\n        \"status\": 0,\n        \"lastLogin\": 1,\n        \"isWaitSeal\": 0,\n        \"isDrop\": 0,\n        \"entryMode\": 0,\n        \"isTry\": 1,\n        \"isShowVersion\": 1,\n        \"showRenewal\": 1,\n        \"showExpireTime\": 1\n    }\n}"}]}, {"name": "离开店铺换取创作者Token", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{shopToken}}", "type": "text", "description": "店铺JWT令牌"}], "url": {"raw": "{{baseUrl}}/creator_shop/leaveShop/changeShopTokenToCreatorToken", "host": ["{{baseUrl}}"], "path": ["creator_shop", "leaveShop", "changeShopTokenToCreatorToken"]}, "description": "离开当前店铺，将shopToken换回creatorToken。用于返回店铺列表或切换到其他店铺。\n\n**使用场景：**\n- 从店铺内部返回到店铺列表\n- 切换到其他店铺\n- 退出店铺管理界面\n\n**功能说明：**\n1. 通过shopToken获取店铺信息\n2. 根据店铺信息获取创作者信息\n3. 生成新的creatorToken\n4. 返回创作者信息和新的creatorToken\n\n**返回数据包含创作者信息和新的creatorToken**"}, "response": [{"name": "切换成功", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{shopToken}}"}], "url": {"raw": "{{baseUrl}}/creator_shop/leaveShop/changeShopTokenToCreatorToken", "host": ["{{baseUrl}}"], "path": ["creator_shop", "leaveShop", "changeShopTokenToCreatorToken"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"操作成功\",\n    \"data\": {\n        \"creatorToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n        \"creatorInfo\": {\n            \"id\": 1,\n            \"creatorId\": \"creator123456\",\n            \"avatarUrl\": \"https://example.com/avatar.jpg\",\n            \"openid\": \"wx_openid_123\",\n            \"unionid\": \"wx_unionid_456\",\n            \"wxAccount\": \"微信用户\",\n            \"phoneNumber\": \"+86-***********\",\n            \"purePhoneNumber\": \"***********\",\n            \"countryCode\": \"+86\",\n            \"wxMaAppid\": \"wx123456789\",\n            \"wxNickName\": \"测试用户\",\n            \"wxHeadimgurl\": \"https://example.com/wx_avatar.jpg\",\n            \"createTime\": \"2025-01-10 09:00:00\",\n            \"updateTime\": \"2025-01-15 10:30:00\"\n        }\n    }\n}"}, {"name": "店铺不存在", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{shopToken}}"}], "url": {"raw": "{{baseUrl}}/creator_shop/leaveShop/changeShopTokenToCreatorToken", "host": ["{{baseUrl}}"], "path": ["creator_shop", "leaveShop", "changeShopTokenToCreatorToken"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"code\": 500,\n    \"msg\": \"店铺不存在\"\n}"}, {"name": "用户不存在", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{shopToken}}"}], "url": {"raw": "{{baseUrl}}/creator_shop/leaveShop/changeShopTokenToCreatorToken", "host": ["{{baseUrl}}"], "path": ["creator_shop", "leaveShop", "changeShopTokenToCreatorToken"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"code\": 500,\n    \"msg\": \"用户不存在\"\n}"}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string", "description": "API基础URL"}, {"key": "creatorToken", "value": "", "type": "string", "description": "创作者JWT令牌"}, {"key": "shopToken", "value": "", "type": "string", "description": "店铺JWT令牌"}]}