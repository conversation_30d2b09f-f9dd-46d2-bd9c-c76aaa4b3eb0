package com.ruoyi.web.controller.system;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.TrialPermission;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Material;
import com.ruoyi.system.service.IMaterialService;

/**
 * 素材 信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/material")
public class MaterialController extends BaseController
{
    @Autowired
    private IMaterialService materialService;

    /**
     * 获取素材列表
     */
    @PreAuthorize("@ss.hasPermi('system:material:list')")
    @TrialPermission(value = "system:material:list", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping("/list")
    public TableDataInfo list(Material material)
    {
        startPage();
        List<Material> list = materialService.selectMaterialList(material);
        return getDataTable(list);
    }

    /**
     * 根据素材编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:material:query')")
    @TrialPermission(value = "system:material:query", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping(value = "/{materialId}")
    public AjaxResult getInfo(@PathVariable String materialId)
    {
        return success(materialService.selectMaterialById(materialId));
    }

    /**
     * 新增素材
     */
    @PreAuthorize("@ss.hasPermi('system:material:add')")
    @TrialPermission(value = "system:material:add", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "素材", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody Material material)
    {
        return toAjax(materialService.insertMaterial(material));
    }

    /**
     * 修改素材
     */
    @PreAuthorize("@ss.hasPermi('system:material:edit')")
    @TrialPermission(value = "system:material:edit", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "素材", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody Material material)
    {
        return toAjax(materialService.updateMaterial(material));
    }

    /**
     * 删除素材
     */
    @PreAuthorize("@ss.hasPermi('system:material:remove')")
    @TrialPermission(value = "system:material:remove", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "素材", businessType = BusinessType.DELETE)
    @DeleteMapping("/{materialIds}")
    public AjaxResult remove(@PathVariable String[] materialIds)
    {
        return toAjax(materialService.deleteMaterialByIds(materialIds));
    }

    /**
     * 根据分类ID获取素材列表
     */
    @PreAuthorize("@ss.hasPermi('system:material:list')")
    @TrialPermission(value = "system:material:list", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping("/category/{categoryId}")
    public AjaxResult listByCategory(@PathVariable Integer categoryId)
    {
        List<Material> list = materialService.selectMaterialListByCategoryId(categoryId);
        return success(list);
    }

    /**
     * 根据类型获取素材列表
     */
    @PreAuthorize("@ss.hasPermi('system:material:list')")
    @TrialPermission(value = "system:material:list", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping("/type/{type}")
    public AjaxResult listByType(@PathVariable Integer type)
    {
        List<Material> list = materialService.selectMaterialListByType(type);
        return success(list);
    }

    /**
     * 增加素材引用数
     */
    @PreAuthorize("@ss.hasPermi('system:material:edit')")
    @TrialPermission(value = "system:material:edit", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "素材引用", businessType = BusinessType.UPDATE)
    @PutMapping("/refer/increase/{materialId}")
    public AjaxResult increaseReferCount(@PathVariable String materialId)
    {
        return toAjax(materialService.increaseReferCount(materialId));
    }

    /**
     * 减少素材引用数
     */
    @PreAuthorize("@ss.hasPermi('system:material:edit')")
    @TrialPermission(value = "system:material:edit", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "素材引用", businessType = BusinessType.UPDATE)
    @PutMapping("/refer/decrease/{materialId}")
    public AjaxResult decreaseReferCount(@PathVariable String materialId)
    {
        return toAjax(materialService.decreaseReferCount(materialId));
    }

    /**
     * 增加素材访问数
     */
    @PreAuthorize("@ss.hasPermi('system:material:view')")
    @TrialPermission(value = "system:material:view", type = 1, trialAvailable = true, expiredAvailable = true)
    @Log(title = "素材访问", businessType = BusinessType.UPDATE)
    @PutMapping("/view/increase/{materialId}")
    public AjaxResult increaseViewCount(@PathVariable String materialId)
    {
        return toAjax(materialService.increaseViewCount(materialId));
    }
}
