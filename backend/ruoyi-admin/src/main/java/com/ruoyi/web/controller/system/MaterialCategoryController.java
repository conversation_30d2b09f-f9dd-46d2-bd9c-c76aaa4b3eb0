package com.ruoyi.web.controller.system;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.TrialPermission;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.MaterialCategory;
import com.ruoyi.system.service.IMaterialCategoryService;

/**
 * 素材分组 信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/materialCategory")
public class MaterialCategoryController extends BaseController
{
    @Autowired
    private IMaterialCategoryService materialCategoryService;

    /**
     * 获取素材分组列表
     */
    @PreAuthorize("@ss.hasPermi('system:materialCategory:list')")
    @TrialPermission(value = "system:materialCategory:list", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping("/list")
    public TableDataInfo list(MaterialCategory materialCategory)
    {
        startPage();
        List<MaterialCategory> list = materialCategoryService.selectMaterialCategoryList(materialCategory);
        return getDataTable(list);
    }

    /**
     * 根据素材分组编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:materialCategory:query')")
    @TrialPermission(value = "system:materialCategory:query", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Integer id)
    {
        return success(materialCategoryService.selectMaterialCategoryById(id));
    }

    /**
     * 新增素材分组
     */
    @PreAuthorize("@ss.hasPermi('system:materialCategory:add')")
    @TrialPermission(value = "system:materialCategory:add", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "素材分组", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MaterialCategory materialCategory)
    {
        if (!materialCategoryService.checkMaterialCategoryNameUnique(materialCategory))
        {
            return error("新增素材分组'" + materialCategory.getName() + "'失败，分组名称已存在");
        }
        return toAjax(materialCategoryService.insertMaterialCategory(materialCategory));
    }

    /**
     * 修改素材分组
     */
    @PreAuthorize("@ss.hasPermi('system:materialCategory:edit')")
    @TrialPermission(value = "system:materialCategory:edit", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "素材分组", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MaterialCategory materialCategory)
    {
        if (!materialCategoryService.checkMaterialCategoryNameUnique(materialCategory))
        {
            return error("修改素材分组'" + materialCategory.getName() + "'失败，分组名称已存在");
        }
        return toAjax(materialCategoryService.updateMaterialCategory(materialCategory));
    }

    /**
     * 删除素材分组
     */
    @PreAuthorize("@ss.hasPermi('system:materialCategory:remove')")
    @TrialPermission(value = "system:materialCategory:remove", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "素材分组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        for (Integer id : ids)
        {
            MaterialCategory materialCategory = materialCategoryService.selectMaterialCategoryById(id);
            if (StringUtils.isNotNull(materialCategory) && materialCategory.getIsAllowDelete() == 0)
            {
                return error("素材分组'" + materialCategory.getName() + "'不允许删除");
            }
            if (materialCategoryService.hasChildByMaterialCategoryId(id))
            {
                return error("存在下级素材分组，不允许删除");
            }
            if (materialCategoryService.checkMaterialCategoryExistMaterial(id))
            {
                return error("素材分组存在素材，不允许删除");
            }
        }
        return toAjax(materialCategoryService.deleteMaterialCategoryByIds(ids));
    }

    /**
     * 根据父分类ID获取子分类列表
     */
    @PreAuthorize("@ss.hasPermi('system:materialCategory:list')")
    @TrialPermission(value = "system:materialCategory:list", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping("/children/{parentId}")
    public AjaxResult listByParent(@PathVariable Integer parentId)
    {
        List<MaterialCategory> list = materialCategoryService.selectMaterialCategoryListByParentId(parentId);
        return success(list);
    }

    /**
     * 根据类型获取分组列表
     */
    @PreAuthorize("@ss.hasPermi('system:materialCategory:list')")
    @TrialPermission(value = "system:materialCategory:list", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping("/type/{type}")
    public AjaxResult listByType(@PathVariable Integer type)
    {
        List<MaterialCategory> list = materialCategoryService.selectMaterialCategoryListByType(type);
        return success(list);
    }

    /**
     * 更新分组素材数量
     */
    @PreAuthorize("@ss.hasPermi('system:materialCategory:edit')")
    @TrialPermission(value = "system:materialCategory:edit", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "素材分组数量", businessType = BusinessType.UPDATE)
    @PutMapping("/count/update/{categoryId}")
    public AjaxResult updateCount(@PathVariable Integer categoryId)
    {
        return toAjax(materialCategoryService.updateMaterialCategoryCount(categoryId));
    }

    /**
     * 增加分组素材数量
     */
    @PreAuthorize("@ss.hasPermi('system:materialCategory:edit')")
    @TrialPermission(value = "system:materialCategory:edit", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "素材分组数量", businessType = BusinessType.UPDATE)
    @PutMapping("/count/increase/{categoryId}")
    public AjaxResult increaseCount(@PathVariable Integer categoryId)
    {
        return toAjax(materialCategoryService.increaseMaterialCategoryCount(categoryId));
    }

    /**
     * 减少分组素材数量
     */
    @PreAuthorize("@ss.hasPermi('system:materialCategory:edit')")
    @TrialPermission(value = "system:materialCategory:edit", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "素材分组数量", businessType = BusinessType.UPDATE)
    @PutMapping("/count/decrease/{categoryId}")
    public AjaxResult decreaseCount(@PathVariable Integer categoryId)
    {
        return toAjax(materialCategoryService.decreaseMaterialCategoryCount(categoryId));
    }
}
