package com.ruoyi.framework.web.jwt;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.StringUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class WendaoShopJwtUtil {

    /**
     * 创作者id
     */
    //public static final String SHOP_ID = "shopId";
    //public static final String CREATOR_ID = "creatorId";
    // 令牌自定义标识
    @Value("${token.header}")
    private String header;

    // 令牌秘钥
    @Value("${token.secret}")
    private String secret;

    // 令牌有效期（默认30分钟）
    @Value("${token.expireTime}")
    private int expireTime;

    /**
     * 生成token
     */
    public String generateToken(String shopId,String creatorId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(Constants.LOGIN_USER_SHOP_ID_KEY, shopId);
        claims.put(Constants.LOGIN_USER_CREATOR_ID_KEY, creatorId);
        return createToken(claims);
    }
    
    /**
     * 从token中获取店铺Id
     */
    public String getShopIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get(Constants.LOGIN_USER_SHOP_ID_KEY, String.class);
    }

    /**
     * 从token中获取创作者Id
     */
    public String getCreatorIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get(Constants.LOGIN_USER_CREATOR_ID_KEY, String.class);
    }
    
    /**
     * 验证token是否有效
     */
    public boolean validateToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            return !claims.getExpiration().before(new Date());
        } catch (Exception e) {
            return false;
        }
    }
    
    private String createToken(Map<String, Object> claims) {
        // 计算过期时间（当前时间 + expireTime 分钟）
        long expirationMillis = expireTime * 60 * 1000L;  // 分钟 → 毫秒
        Date expirationDate = new Date(System.currentTimeMillis() + expirationMillis);

        return Jwts.builder()
                .setClaims(claims)
                .setExpiration(expirationDate)  // 设置过期时间
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }
    
    private Claims getClaimsFromToken(String token) {
        return Jwts.parser()
            .setSigningKey(secret)
            .parseClaimsJws(token)
            .getBody();
    }
    

} 