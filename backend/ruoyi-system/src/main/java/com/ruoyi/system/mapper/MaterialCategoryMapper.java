package com.ruoyi.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.MaterialCategory;

/**
 * 素材分组表 数据层
 * 
 * <AUTHOR>
 */
public interface MaterialCategoryMapper
{
    /**
     * 查询素材分组信息
     * 
     * @param id 素材分组ID
     * @return 素材分组信息
     */
    public MaterialCategory selectMaterialCategoryById(Integer id);

    /**
     * 查询素材分组列表
     * 
     * @param materialCategory 素材分组信息
     * @return 素材分组集合
     */
    public List<MaterialCategory> selectMaterialCategoryList(MaterialCategory materialCategory);

    /**
     * 新增素材分组
     * 
     * @param materialCategory 素材分组信息
     * @return 结果
     */
    public int insertMaterialCategory(MaterialCategory materialCategory);

    /**
     * 修改素材分组
     * 
     * @param materialCategory 素材分组信息
     * @return 结果
     */
    public int updateMaterialCategory(MaterialCategory materialCategory);

    /**
     * 删除素材分组
     * 
     * @param id 素材分组ID
     * @return 结果
     */
    public int deleteMaterialCategoryById(Integer id);

    /**
     * 批量删除素材分组信息
     * 
     * @param ids 需要删除的素材分组ID
     * @return 结果
     */
    public int deleteMaterialCategoryByIds(Integer[] ids);

    /**
     * 根据父分类ID查询子分类列表
     * 
     * @param parentId 父分类ID
     * @return 子分类集合
     */
    public List<MaterialCategory> selectMaterialCategoryListByParentId(Integer parentId);

    /**
     * 根据类型查询分组列表
     * 
     * @param type 分组类型
     * @return 分组集合
     */
    public List<MaterialCategory> selectMaterialCategoryListByType(Integer type);

    /**
     * 检查分组名称是否唯一
     * 
     * @param name 分组名称
     * @param parentId 父分组ID
     * @param type 分组类型
     * @return 素材分组信息
     */
    public MaterialCategory checkMaterialCategoryNameUnique(@Param("name") String name, @Param("parentId") Integer parentId, @Param("type") Integer type);

    /**
     * 检查是否存在子分组
     * 
     * @param id 分组ID
     * @return 结果
     */
    public int hasChildByMaterialCategoryId(Integer id);

    /**
     * 检查分组下是否存在素材
     * 
     * @param id 分组ID
     * @return 结果
     */
    public int checkMaterialCategoryExistMaterial(Integer id);

    /**
     * 更新分组素材数量
     * 
     * @param categoryId 分组ID
     * @return 结果
     */
    public int updateMaterialCategoryCount(Integer categoryId);

    /**
     * 增加分组素材数量
     * 
     * @param categoryId 分组ID
     * @return 结果
     */
    public int increaseMaterialCategoryCount(Integer categoryId);

    /**
     * 减少分组素材数量
     * 
     * @param categoryId 分组ID
     * @return 结果
     */
    public int decreaseMaterialCategoryCount(Integer categoryId);
}
