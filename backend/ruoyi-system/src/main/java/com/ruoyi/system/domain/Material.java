package com.ruoyi.system.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.ruoyi.common.annotation.TableId;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.xss.Xss;

/**
 * 素材存储表 material
 * 
 * <AUTHOR>
 */
public class Material extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 素材ID */
    @TableId
    private String materialId;

    /** 素材名称 */
    private String title;

    /** 素材类型:1-图片 2-音频 3-视频 4-电子书 5-文档 */
    private Integer type;

    /** 分组ID */
    private Integer categoryId;

    /** 素材大小(KB) */
    private Double materialSize;

    /** 被引用数 */
    private Integer referCount;

    /** 访问数 */
    private Integer viewCount;

    /** 宽(px) */
    private Integer width;

    /** 高(px) */
    private Integer height;

    /** 时长(秒) */
    private Double length;

    /** 封面贴图URL */
    private String patchImgUrl;

    /** 分辨率(如464 * 640) */
    private String pixelData;

    public String getMaterialId()
    {
        return materialId;
    }

    public void setMaterialId(String materialId)
    {
        this.materialId = materialId;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    @Xss(message = "素材名称不能包含脚本字符")
    @NotBlank(message = "素材名称不能为空")
    @Size(min = 0, max = 255, message = "素材名称不能超过255个字符")
    public String getTitle()
    {
        return title;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }

    @NotNull(message = "素材类型不能为空")
    public Integer getType()
    {
        return type;
    }

    public void setCategoryId(Integer categoryId)
    {
        this.categoryId = categoryId;
    }

    @NotNull(message = "分组ID不能为空")
    public Integer getCategoryId()
    {
        return categoryId;
    }

    public void setMaterialSize(Double materialSize)
    {
        this.materialSize = materialSize;
    }

    public Double getMaterialSize()
    {
        return materialSize;
    }

    public void setReferCount(Integer referCount)
    {
        this.referCount = referCount;
    }

    public Integer getReferCount()
    {
        return referCount;
    }

    public void setViewCount(Integer viewCount)
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount()
    {
        return viewCount;
    }

    public void setWidth(Integer width)
    {
        this.width = width;
    }

    public Integer getWidth()
    {
        return width;
    }

    public void setHeight(Integer height)
    {
        this.height = height;
    }

    public Integer getHeight()
    {
        return height;
    }

    public void setLength(Double length)
    {
        this.length = length;
    }

    public Double getLength()
    {
        return length;
    }

    public void setPatchImgUrl(String patchImgUrl)
    {
        this.patchImgUrl = patchImgUrl;
    }

    @Size(min = 0, max = 255, message = "封面贴图URL不能超过255个字符")
    public String getPatchImgUrl()
    {
        return patchImgUrl;
    }

    public void setPixelData(String pixelData)
    {
        this.pixelData = pixelData;
    }

    @Size(min = 0, max = 32, message = "分辨率不能超过32个字符")
    public String getPixelData()
    {
        return pixelData;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("materialId", getMaterialId())
            .append("title", getTitle())
            .append("type", getType())
            .append("categoryId", getCategoryId())
            .append("materialSize", getMaterialSize())
            .append("referCount", getReferCount())
            .append("viewCount", getViewCount())
            .append("width", getWidth())
            .append("height", getHeight())
            .append("length", getLength())
            .append("patchImgUrl", getPatchImgUrl())
            .append("pixelData", getPixelData())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
