package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.Material;

/**
 * 素材 服务层
 * 
 * <AUTHOR>
 */
public interface IMaterialService
{
    /**
     * 查询素材信息
     * 
     * @param materialId 素材ID
     * @return 素材信息
     */
    public Material selectMaterialById(String materialId);

    /**
     * 查询素材列表
     * 
     * @param material 素材信息
     * @return 素材集合
     */
    public List<Material> selectMaterialList(Material material);

    /**
     * 新增素材
     * 
     * @param material 素材信息
     * @return 结果
     */
    public int insertMaterial(Material material);

    /**
     * 修改素材
     * 
     * @param material 素材信息
     * @return 结果
     */
    public int updateMaterial(Material material);

    /**
     * 删除素材信息
     * 
     * @param materialId 素材ID
     * @return 结果
     */
    public int deleteMaterialById(String materialId);
    
    /**
     * 批量删除素材信息
     * 
     * @param materialIds 需要删除的素材ID
     * @return 结果
     */
    public int deleteMaterialByIds(String[] materialIds);

    /**
     * 增加素材引用数
     * 
     * @param materialId 素材ID
     * @return 结果
     */
    public int increaseReferCount(String materialId);

    /**
     * 减少素材引用数
     * 
     * @param materialId 素材ID
     * @return 结果
     */
    public int decreaseReferCount(String materialId);

    /**
     * 增加素材访问数
     * 
     * @param materialId 素材ID
     * @return 结果
     */
    public int increaseViewCount(String materialId);

    /**
     * 根据分类ID查询素材列表
     * 
     * @param categoryId 分类ID
     * @return 素材集合
     */
    public List<Material> selectMaterialListByCategoryId(Integer categoryId);

    /**
     * 根据类型查询素材列表
     * 
     * @param type 素材类型
     * @return 素材集合
     */
    public List<Material> selectMaterialListByType(Integer type);
}
