package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.domain.Material;
import com.ruoyi.system.mapper.MaterialMapper;
import com.ruoyi.system.service.IMaterialService;

/**
 * 素材 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class MaterialServiceImpl implements IMaterialService
{
    @Autowired
    private MaterialMapper materialMapper;

    /**
     * 查询素材信息
     * 
     * @param materialId 素材ID
     * @return 素材信息
     */
    @Override
    public Material selectMaterialById(String materialId)
    {
        return materialMapper.selectMaterialById(materialId);
    }

    /**
     * 查询素材列表
     * 
     * @param material 素材信息
     * @return 素材集合
     */
    @Override
    public List<Material> selectMaterialList(Material material)
    {
        return materialMapper.selectMaterialList(material);
    }

    /**
     * 新增素材
     * 
     * @param material 素材信息
     * @return 结果
     */
    @Override
    public int insertMaterial(Material material)
    {
        return materialMapper.insertMaterial(material);
    }

    /**
     * 修改素材
     * 
     * @param material 素材信息
     * @return 结果
     */
    @Override
    public int updateMaterial(Material material)
    {
        return materialMapper.updateMaterial(material);
    }

    /**
     * 删除素材对象
     * 
     * @param materialId 素材ID
     * @return 结果
     */
    @Override
    public int deleteMaterialById(String materialId)
    {
        return materialMapper.deleteMaterialById(materialId);
    }

    /**
     * 批量删除素材信息
     * 
     * @param materialIds 需要删除的素材ID
     * @return 结果
     */
    @Override
    public int deleteMaterialByIds(String[] materialIds)
    {
        return materialMapper.deleteMaterialByIds(materialIds);
    }

    /**
     * 增加素材引用数
     * 
     * @param materialId 素材ID
     * @return 结果
     */
    @Override
    public int increaseReferCount(String materialId)
    {
        return materialMapper.increaseReferCount(materialId);
    }

    /**
     * 减少素材引用数
     * 
     * @param materialId 素材ID
     * @return 结果
     */
    @Override
    public int decreaseReferCount(String materialId)
    {
        return materialMapper.decreaseReferCount(materialId);
    }

    /**
     * 增加素材访问数
     * 
     * @param materialId 素材ID
     * @return 结果
     */
    @Override
    public int increaseViewCount(String materialId)
    {
        return materialMapper.increaseViewCount(materialId);
    }

    /**
     * 根据分类ID查询素材列表
     * 
     * @param categoryId 分类ID
     * @return 素材集合
     */
    @Override
    public List<Material> selectMaterialListByCategoryId(Integer categoryId)
    {
        return materialMapper.selectMaterialListByCategoryId(categoryId);
    }

    /**
     * 根据类型查询素材列表
     * 
     * @param type 素材类型
     * @return 素材集合
     */
    @Override
    public List<Material> selectMaterialListByType(Integer type)
    {
        return materialMapper.selectMaterialListByType(type);
    }
}
