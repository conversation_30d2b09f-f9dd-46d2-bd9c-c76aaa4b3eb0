package com.ruoyi.system.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.ruoyi.common.annotation.TableId;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 普通店铺信息对象 wendao_shop
 */
public class WendaoShop extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    @TableId
    private Long id;

    /** 店主ID（关联店主表） */
    @Excel(name = "店主ID")
    private String creatorId;

    /** 店铺创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "店铺创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /** 服务到期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "服务到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireTime;

    /** 是否被封禁(0=未封禁,1=封禁) */
    @Excel(name = "是否被封禁", readConverterExp = "0=未封禁,1=封禁")
    private Integer isSealed;

    /** 店铺唯一ID */
    @Excel(name = "店铺唯一ID")
    private String shopId;

    /** 店铺LOGO地址 */
    @Excel(name = "店铺LOGO地址")
    private String shopLogo;

    /** 店铺名称 */
    @Excel(name = "店铺名称")
    private String shopName;

    /** 是否使用收藏功能 */
    @Excel(name = "是否使用收藏功能", readConverterExp = "0=否,1=是")
    private Integer useCollection;

    /** 版本类型:0-试用版,1-标准版,2-专业版,3-旗舰版 */
    @Excel(name = "版本类型", readConverterExp = "0=试用版,1=标准版,2=专业版,3=旗舰版")
    private Integer versionType;

    /** 权益类型(-1=无权益) */
    @Excel(name = "权益类型")
    private Integer rightsType;

    /** 应用ID（和店铺ID一致） */
    @Excel(name = "应用ID")
    private String appId;

    /** 是否已过期(0=未过期,1=过期) */
    @Excel(name = "是否已过期", readConverterExp = "0=未过期,1=过期")
    private Integer hasExpired;

    /** 是否有未支付激活订单(购买版本的订单) */
    @Excel(name = "是否有未支付激活订单", readConverterExp = "0=否,1=是")
    private Integer hasActivateOrder;

    /** 店铺状态:0-正常,1-已关闭 */
    @Excel(name = "店铺状态", readConverterExp = "0=正常,1=已关闭")
    private Integer status;

    /** 是否最后登录店铺 */
    @Excel(name = "是否最后登录店铺", readConverterExp = "0=否,1=是")
    private Integer lastLogin;

    /** 是否等待封禁中 */
    @Excel(name = "是否等待封禁中", readConverterExp = "0=否,1=是")
    private Integer isWaitSeal;

    /** 封禁申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "封禁申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date sealApplyTime;

    /** 准备封禁时间,预计封停时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "准备封禁时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date readySealAt;

    /** 是否已注销,0未注销,1已注销,已注销查询列表不显示 */
    @Excel(name = "是否已注销", readConverterExp = "0=未注销,1=已注销")
    private Integer isDrop;

    /** 进入模式 */
    @Excel(name = "进入模式")
    private Integer entryMode;

    /** 是否试用店铺,0不是,1是 */
    @Excel(name = "是否试用店铺", readConverterExp = "0=不是,1=是")
    private Integer isTry;

    /** 是否显示版本信息 */
    @Excel(name = "是否显示版本信息", readConverterExp = "0=否,1=是")
    private Integer isShowVersion;

    /** 是否显示续费按钮 */
    @Excel(name = "是否显示续费按钮", readConverterExp = "0=否,1=是")
    private Integer showRenewal;

    /** 是否显示到期时间 */
    @Excel(name = "是否显示到期时间", readConverterExp = "0=否,1=是")
    private Integer showExpireTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @NotBlank(message = "店主ID不能为空")
    @Size(min = 0, max = 64, message = "店主ID长度不能超过64个字符")
    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    @NotNull(message = "店铺创建时间不能为空")
    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @NotNull(message = "服务到期日期不能为空")
    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Integer getIsSealed() {
        return isSealed;
    }

    public void setIsSealed(Integer isSealed) {
        this.isSealed = isSealed;
    }

    @NotBlank(message = "店铺唯一ID不能为空")
    @Size(min = 0, max = 32, message = "店铺唯一ID长度不能超过32个字符")
    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    @Size(min = 0, max = 255, message = "店铺LOGO地址长度不能超过255个字符")
    public String getShopLogo() {
        return shopLogo;
    }

    public void setShopLogo(String shopLogo) {
        this.shopLogo = shopLogo;
    }

    @NotBlank(message = "店铺名称不能为空")
    @Size(min = 0, max = 100, message = "店铺名称长度不能超过100个字符")
    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Integer getUseCollection() {
        return useCollection;
    }

    public void setUseCollection(Integer useCollection) {
        this.useCollection = useCollection;
    }

    @NotNull(message = "版本类型不能为空")
    public Integer getVersionType() {
        return versionType;
    }

    public void setVersionType(Integer versionType) {
        this.versionType = versionType;
    }

    @NotNull(message = "权益类型不能为空")
    public Integer getRightsType() {
        return rightsType;
    }

    public void setRightsType(Integer rightsType) {
        this.rightsType = rightsType;
    }

    @NotBlank(message = "应用ID不能为空")
    @Size(min = 0, max = 32, message = "应用ID长度不能超过32个字符")
    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Integer getHasExpired() {
        return hasExpired;
    }

    public void setHasExpired(Integer hasExpired) {
        this.hasExpired = hasExpired;
    }

    public Integer getHasActivateOrder() {
        return hasActivateOrder;
    }

    public void setHasActivateOrder(Integer hasActivateOrder) {
        this.hasActivateOrder = hasActivateOrder;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getLastLogin() {
        return lastLogin;
    }

    public void setLastLogin(Integer lastLogin) {
        this.lastLogin = lastLogin;
    }

    public Integer getIsWaitSeal() {
        return isWaitSeal;
    }

    public void setIsWaitSeal(Integer isWaitSeal) {
        this.isWaitSeal = isWaitSeal;
    }

    public Date getSealApplyTime() {
        return sealApplyTime;
    }

    public void setSealApplyTime(Date sealApplyTime) {
        this.sealApplyTime = sealApplyTime;
    }

    public Date getReadySealAt() {
        return readySealAt;
    }

    public void setReadySealAt(Date readySealAt) {
        this.readySealAt = readySealAt;
    }

    public Integer getIsDrop() {
        return isDrop;
    }

    public void setIsDrop(Integer isDrop) {
        this.isDrop = isDrop;
    }

    public Integer getEntryMode() {
        return entryMode;
    }

    public void setEntryMode(Integer entryMode) {
        this.entryMode = entryMode;
    }

    public Integer getIsTry() {
        return isTry;
    }

    public void setIsTry(Integer isTry) {
        this.isTry = isTry;
    }

    public Integer getIsShowVersion() {
        return isShowVersion;
    }

    public void setIsShowVersion(Integer isShowVersion) {
        this.isShowVersion = isShowVersion;
    }

    public Integer getShowRenewal() {
        return showRenewal;
    }

    public void setShowRenewal(Integer showRenewal) {
        this.showRenewal = showRenewal;
    }

    public Integer getShowExpireTime() {
        return showExpireTime;
    }

    public void setShowExpireTime(Integer showExpireTime) {
        this.showExpireTime = showExpireTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("creatorId", getCreatorId())
                .append("createdAt", getCreatedAt())
                .append("expireTime", getExpireTime())
                .append("isSealed", getIsSealed())
                .append("shopId", getShopId())
                .append("shopLogo", getShopLogo())
                .append("shopName", getShopName())
                .append("useCollection", getUseCollection())
                .append("versionType", getVersionType())
                .append("rightsType", getRightsType())
                .append("appId", getAppId())
                .append("hasExpired", getHasExpired())
                .append("hasActivateOrder", getHasActivateOrder())
                .append("status", getStatus())
                .append("lastLogin", getLastLogin())
                .append("isWaitSeal", getIsWaitSeal())
                .append("sealApplyTime", getSealApplyTime())
                .append("readySealAt", getReadySealAt())
                .append("isDrop", getIsDrop())
                .append("entryMode", getEntryMode())
                .append("isTry", getIsTry())
                .append("isShowVersion", getIsShowVersion())
                .append("showRenewal", getShowRenewal())
                .append("showExpireTime", getShowExpireTime())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
