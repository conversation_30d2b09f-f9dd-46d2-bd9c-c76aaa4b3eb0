package com.ruoyi.system.service;

import java.util.List;

import com.ruoyi.system.domain.MigrationLog;
import com.ruoyi.system.domain.WendaoShop;

/**
 * 普通店铺信息管理 服务层
 */
public interface IWendaoShopService {
    /**
     * 查询普通店铺信息
     */
    public WendaoShop selectWendaoShopById(Long id);

    /**
     * 根据店铺ID查询普通店铺信息
     */
    public WendaoShop selectWendaoShopByShopId(String shopId);

    /**
     * 根据应用ID查询普通店铺信息
     */
    public WendaoShop selectWendaoShopByAppId(String appId);

    /**
     * 根据店主ID查询普通店铺信息
     */
    public WendaoShop selectWendaoShopByCreatorId(String creatorId);

    /**
     * 根据店主ID查询普通店铺信息列表
     */
    public List<WendaoShop> selectWendaoShopListByCreatorId(String creatorId);

    /**
     * 查询普通店铺信息列表
     */
    public List<WendaoShop> selectWendaoShopList(WendaoShop wendaoShop);

    /**
     * 新增普通店铺信息
     */
    public int insertWendaoShop(WendaoShop wendaoShop);

    /**
     * 修改普通店铺信息
     */
    public int updateWendaoShop(WendaoShop wendaoShop);

    /**
     * 删除普通店铺信息
     */
    public int deleteWendaoShopByIds(Long[] ids);

    /**
     * 删除普通店铺信息
     */
    public int deleteWendaoShopById(Long id);

    /**
     * 校验店铺ID是否唯一
     */
    public boolean checkShopIdUnique(String shopId);

    /**
     * 校验应用ID是否唯一
     */
    public boolean checkAppIdUnique(String appId);

    /**
     * 封禁店铺
     */
    public int sealShop(Long id);

    /**
     * 解封店铺
     */
    public int unsealShop(Long id);

    /**
     * 关闭店铺
     */
    public int closeShop(Long id);

    /**
     * 开启店铺
     */
    public int openShop(Long id);

    /**
     * 注销店铺
     */
    public int dropShop(Long id);

    /**
     * 恢复店铺
     */
    public int restoreShop(Long id);

    /**
     * 更新店铺过期状态
     */
    public int updateExpiredStatus();

    /**
     * 查询即将过期的店铺列表
     */
    public List<WendaoShop> selectExpiringSoonShops(int days);

    /**
     * 查询已过期的店铺列表
     */
    public List<WendaoShop> selectExpiredShops();

    /**
     * 设置最后登录店铺
     */
    public int setLastLoginShop(String creatorId, String shopId);

    WendaoShop selectWendaoShopByShopIdAndCreatorId(String creatorId, String shopId);

    List<MigrationLog> callMigrateShopData(String sourceDb,
                                           String oldShopId,
                                           String newShopId,
                                           String userName,
                                           String nickName,
                                           String phonenumber);
}
