package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.MaterialCategory;
import com.ruoyi.system.mapper.MaterialCategoryMapper;
import com.ruoyi.system.service.IMaterialCategoryService;

/**
 * 素材分组 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class MaterialCategoryServiceImpl implements IMaterialCategoryService
{
    @Autowired
    private MaterialCategoryMapper materialCategoryMapper;

    /**
     * 查询素材分组信息
     * 
     * @param id 素材分组ID
     * @return 素材分组信息
     */
    @Override
    public MaterialCategory selectMaterialCategoryById(Integer id)
    {
        return materialCategoryMapper.selectMaterialCategoryById(id);
    }

    /**
     * 查询素材分组列表
     * 
     * @param materialCategory 素材分组信息
     * @return 素材分组集合
     */
    @Override
    public List<MaterialCategory> selectMaterialCategoryList(MaterialCategory materialCategory)
    {
        return materialCategoryMapper.selectMaterialCategoryList(materialCategory);
    }

    /**
     * 新增素材分组
     * 
     * @param materialCategory 素材分组信息
     * @return 结果
     */
    @Override
    public int insertMaterialCategory(MaterialCategory materialCategory)
    {
        return materialCategoryMapper.insertMaterialCategory(materialCategory);
    }

    /**
     * 修改素材分组
     * 
     * @param materialCategory 素材分组信息
     * @return 结果
     */
    @Override
    public int updateMaterialCategory(MaterialCategory materialCategory)
    {
        return materialCategoryMapper.updateMaterialCategory(materialCategory);
    }

    /**
     * 删除素材分组对象
     * 
     * @param id 素材分组ID
     * @return 结果
     */
    @Override
    public int deleteMaterialCategoryById(Integer id)
    {
        return materialCategoryMapper.deleteMaterialCategoryById(id);
    }

    /**
     * 批量删除素材分组信息
     * 
     * @param ids 需要删除的素材分组ID
     * @return 结果
     */
    @Override
    public int deleteMaterialCategoryByIds(Integer[] ids)
    {
        return materialCategoryMapper.deleteMaterialCategoryByIds(ids);
    }

    /**
     * 根据父分类ID查询子分类列表
     * 
     * @param parentId 父分类ID
     * @return 子分类集合
     */
    @Override
    public List<MaterialCategory> selectMaterialCategoryListByParentId(Integer parentId)
    {
        return materialCategoryMapper.selectMaterialCategoryListByParentId(parentId);
    }

    /**
     * 根据类型查询分组列表
     * 
     * @param type 分组类型
     * @return 分组集合
     */
    @Override
    public List<MaterialCategory> selectMaterialCategoryListByType(Integer type)
    {
        return materialCategoryMapper.selectMaterialCategoryListByType(type);
    }

    /**
     * 检查分组名称是否唯一
     * 
     * @param materialCategory 素材分组信息
     * @return 结果
     */
    @Override
    public boolean checkMaterialCategoryNameUnique(MaterialCategory materialCategory)
    {
        Integer categoryId = StringUtils.isNull(materialCategory.getId()) ? -1 : materialCategory.getId();
        MaterialCategory info = materialCategoryMapper.checkMaterialCategoryNameUnique(materialCategory.getName(), materialCategory.getParentId(), materialCategory.getType());
        if (StringUtils.isNotNull(info) && info.getId().intValue() != categoryId.intValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 检查是否存在子分组
     * 
     * @param id 分组ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean hasChildByMaterialCategoryId(Integer id)
    {
        int result = materialCategoryMapper.hasChildByMaterialCategoryId(id);
        return result > 0;
    }

    /**
     * 检查分组下是否存在素材
     * 
     * @param id 分组ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkMaterialCategoryExistMaterial(Integer id)
    {
        int result = materialCategoryMapper.checkMaterialCategoryExistMaterial(id);
        return result > 0;
    }

    /**
     * 更新分组素材数量
     * 
     * @param categoryId 分组ID
     * @return 结果
     */
    @Override
    public int updateMaterialCategoryCount(Integer categoryId)
    {
        return materialCategoryMapper.updateMaterialCategoryCount(categoryId);
    }

    /**
     * 增加分组素材数量
     * 
     * @param categoryId 分组ID
     * @return 结果
     */
    @Override
    public int increaseMaterialCategoryCount(Integer categoryId)
    {
        return materialCategoryMapper.increaseMaterialCategoryCount(categoryId);
    }

    /**
     * 减少分组素材数量
     * 
     * @param categoryId 分组ID
     * @return 结果
     */
    @Override
    public int decreaseMaterialCategoryCount(Integer categoryId)
    {
        return materialCategoryMapper.decreaseMaterialCategoryCount(categoryId);
    }
}
