<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MaterialCategoryMapper">
    
    <resultMap type="MaterialCategory" id="MaterialCategoryResult">
        <result property="id"               column="id"               />
        <result property="parentId"         column="parent_id"        />
        <result property="name"             column="name"             />
        <result property="type"             column="type"             />
        <result property="categoryCount"    column="category_count"   />
        <result property="sortOrder"        column="sort_order"       />
        <result property="createTime"       column="create_time"      />
        <result property="updateTime"       column="update_time"      />
        <result property="isAllowDelete"    column="is_allow_delete"  />
    </resultMap>
    
    <sql id="selectMaterialCategoryVo">
        select id, parent_id, name, type, category_count, sort_order, create_time, update_time, is_allow_delete 
        from material_category
    </sql>
    
    <select id="selectMaterialCategoryById" parameterType="Integer" resultMap="MaterialCategoryResult">
        <include refid="selectMaterialCategoryVo"/>
        where id = #{id}
    </select>
    
    <select id="selectMaterialCategoryList" parameterType="MaterialCategory" resultMap="MaterialCategoryResult">
        <include refid="selectMaterialCategoryVo"/>
        <where>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="type != null">
                AND type = #{type}
            </if>
            <if test="parentId != null">
                AND parent_id = #{parentId}
            </if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMaterialCategoryListByParentId" parameterType="Integer" resultMap="MaterialCategoryResult">
        <include refid="selectMaterialCategoryVo"/>
        where parent_id = #{parentId}
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMaterialCategoryListByType" parameterType="Integer" resultMap="MaterialCategoryResult">
        <include refid="selectMaterialCategoryVo"/>
        where type = #{type}
        order by sort_order asc, create_time desc
    </select>
    
    <select id="checkMaterialCategoryNameUnique" resultMap="MaterialCategoryResult">
        <include refid="selectMaterialCategoryVo"/>
        where name = #{name} and parent_id = #{parentId} and type = #{type} limit 1
    </select>
    
    <select id="hasChildByMaterialCategoryId" parameterType="Integer" resultType="int">
        select count(1) from material_category where parent_id = #{id} limit 1
    </select>
    
    <select id="checkMaterialCategoryExistMaterial" parameterType="Integer" resultType="int">
        select count(1) from material where category_id = #{id} limit 1
    </select>
    
    <insert id="insertMaterialCategory" parameterType="MaterialCategory" useGeneratedKeys="true" keyProperty="id">
        insert into material_category(
            <if test="parentId != null">parent_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="type != null">type,</if>
            <if test="categoryCount != null">category_count,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="isAllowDelete != null">is_allow_delete,</if>
            create_time
        )values(
            <if test="parentId != null">#{parentId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="categoryCount != null">#{categoryCount},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="isAllowDelete != null">#{isAllowDelete},</if>
            sysdate()
        )
    </insert>
     
    <update id="updateMaterialCategory" parameterType="MaterialCategory">
        update material_category 
        <set>
            <if test="parentId != null">parent_id = #{parentId}, </if>
            <if test="name != null and name != ''">name = #{name}, </if>
            <if test="type != null">type = #{type}, </if>
            <if test="categoryCount != null">category_count = #{categoryCount}, </if>
            <if test="sortOrder != null">sort_order = #{sortOrder}, </if>
            <if test="isAllowDelete != null">is_allow_delete = #{isAllowDelete}, </if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>
    
    <delete id="deleteMaterialCategoryById" parameterType="Integer">
        delete from material_category where id = #{id}
    </delete>
    
    <delete id="deleteMaterialCategoryByIds" parameterType="Integer">
        delete from material_category where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <update id="updateMaterialCategoryCount" parameterType="Integer">
        update material_category set 
            category_count = (select count(1) from material where category_id = #{categoryId}),
            update_time = sysdate()
        where id = #{categoryId}
    </update>
    
    <update id="increaseMaterialCategoryCount" parameterType="Integer">
        update material_category set category_count = category_count + 1, update_time = sysdate() 
        where id = #{categoryId}
    </update>
    
    <update id="decreaseMaterialCategoryCount" parameterType="Integer">
        update material_category set category_count = category_count - 1, update_time = sysdate() 
        where id = #{categoryId} and category_count > 0
    </update>
    
</mapper>
