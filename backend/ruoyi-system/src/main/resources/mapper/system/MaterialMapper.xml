<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MaterialMapper">
    
    <resultMap type="Material" id="MaterialResult">
        <result property="materialId"       column="material_id"       />
        <result property="title"            column="title"             />
        <result property="type"             column="type"              />
        <result property="categoryId"       column="category_id"       />
        <result property="materialSize"     column="material_size"     />
        <result property="referCount"       column="refer_count"       />
        <result property="viewCount"        column="view_count"        />
        <result property="width"            column="width"             />
        <result property="height"           column="height"            />
        <result property="length"           column="length"            />
        <result property="patchImgUrl"      column="patch_img_url"     />
        <result property="pixelData"        column="pixel_data"        />
        <result property="createTime"       column="create_time"       />
        <result property="updateTime"       column="update_time"       />
    </resultMap>
    
    <sql id="selectMaterialVo">
        select material_id, title, type, category_id, material_size, refer_count, view_count, 
               width, height, length, patch_img_url, pixel_data, create_time, update_time 
        from material
    </sql>
    
    <select id="selectMaterialById" parameterType="String" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        where material_id = #{materialId}
    </select>
    
    <select id="selectMaterialList" parameterType="Material" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        <where>
            <if test="title != null and title != ''">
                AND title like concat('%', #{title}, '%')
            </if>
            <if test="type != null">
                AND type = #{type}
            </if>
            <if test="categoryId != null">
                AND category_id = #{categoryId}
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMaterialListByCategoryId" parameterType="Integer" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        where category_id = #{categoryId}
        order by create_time desc
    </select>
    
    <select id="selectMaterialListByType" parameterType="Integer" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        where type = #{type}
        order by create_time desc
    </select>
    
    <insert id="insertMaterial" parameterType="Material">
        insert into material(
            <if test="materialId != null and materialId != ''">material_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="type != null">type,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="materialSize != null">material_size,</if>
            <if test="referCount != null">refer_count,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="width != null">width,</if>
            <if test="height != null">height,</if>
            <if test="length != null">length,</if>
            <if test="patchImgUrl != null and patchImgUrl != ''">patch_img_url,</if>
            <if test="pixelData != null and pixelData != ''">pixel_data,</if>
            create_time
        )values(
            <if test="materialId != null and materialId != ''">#{materialId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="type != null">#{type},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="materialSize != null">#{materialSize},</if>
            <if test="referCount != null">#{referCount},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="width != null">#{width},</if>
            <if test="height != null">#{height},</if>
            <if test="length != null">#{length},</if>
            <if test="patchImgUrl != null and patchImgUrl != ''">#{patchImgUrl},</if>
            <if test="pixelData != null and pixelData != ''">#{pixelData},</if>
            sysdate()
        )
    </insert>
     
    <update id="updateMaterial" parameterType="Material">
        update material 
        <set>
            <if test="title != null and title != ''">title = #{title}, </if>
            <if test="type != null">type = #{type}, </if>
            <if test="categoryId != null">category_id = #{categoryId}, </if>
            <if test="materialSize != null">material_size = #{materialSize}, </if>
            <if test="referCount != null">refer_count = #{referCount}, </if>
            <if test="viewCount != null">view_count = #{viewCount}, </if>
            <if test="width != null">width = #{width}, </if>
            <if test="height != null">height = #{height}, </if>
            <if test="length != null">length = #{length}, </if>
            <if test="patchImgUrl != null">patch_img_url = #{patchImgUrl}, </if>
            <if test="pixelData != null">pixel_data = #{pixelData}, </if>
            update_time = sysdate()
        </set>
        where material_id = #{materialId}
    </update>
    
    <delete id="deleteMaterialById" parameterType="String">
        delete from material where material_id = #{materialId}
    </delete>
    
    <delete id="deleteMaterialByIds" parameterType="String">
        delete from material where material_id in 
        <foreach item="materialId" collection="array" open="(" separator="," close=")">
            #{materialId}
        </foreach>
    </delete>
    
    <update id="increaseReferCount" parameterType="String">
        update material set refer_count = refer_count + 1, update_time = sysdate() 
        where material_id = #{materialId}
    </update>
    
    <update id="decreaseReferCount" parameterType="String">
        update material set refer_count = refer_count - 1, update_time = sysdate() 
        where material_id = #{materialId} and refer_count > 0
    </update>
    
    <update id="increaseViewCount" parameterType="String">
        update material set view_count = view_count + 1, update_time = sysdate() 
        where material_id = #{materialId}
    </update>
    
</mapper>
