-- 素材管理菜单权限SQL
-- 包含Material(素材)和MaterialCategory(素材分组)的菜单和权限配置

-- 素材管理主菜单 (假设parent_id为1，即系统管理下)
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (2001, '素材管理', 1, 9, 'material', 'workbench/material/index', '', '', 1, 0, 'C', '0', '0', 'system:material:list', 'documentation', 'admin', NOW(), '', NULL, '素材管理菜单');

-- 素材管理按钮权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (2002, '素材查询', 2001, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:material:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (2003, '素材新增', 2001, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:material:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (2004, '素材修改', 2001, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:material:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (2005, '素材删除', 2001, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:material:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (2006, '素材访问', 2001, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:material:view', '#', 'admin', NOW(), '', NULL, '');

-- 素材分组管理主菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (2007, '素材分组', 1, 10, 'materialcategory', 'workbench/materialcategory/index', '', '', 1, 0, 'C', '0', '0', 'system:materialCategory:list', 'tree-table', 'admin', NOW(), '', NULL, '素材分组管理菜单');

-- 素材分组管理按钮权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (2008, '分组查询', 2007, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:materialCategory:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (2009, '分组新增', 2007, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:materialCategory:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (2010, '分组修改', 2007, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:materialCategory:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (2011, '分组删除', 2007, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:materialCategory:remove', '#', 'admin', NOW(), '', NULL, '');

-- 为管理员角色(role_id=1)分配所有权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 2001);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 2002);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 2003);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 2004);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 2005);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 2006);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 2007);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 2008);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 2009);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 2010);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 2011);
