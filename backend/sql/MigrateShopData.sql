CREATE DEFINER=`wendao`@`%` PROCEDURE `MigrateShopData`(
    IN source_db VARCHAR(64),
    IN old_shop_id VARCHAR(50),
    IN new_shop_id VARCHAR(50),
    IN user_name VARCHAR(50),    -- 新增用户名参数
    IN nick_name VARCHAR(50),     -- 新增昵称参数
    IN phonenumber VARCHAR(20)    -- 新增电话号码参数
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE tbl_name VARCHAR(64);
    DECLARE col_list TEXT;
    DECLARE has_shop_id BOOLEAN;

    -- 需要处理的表列表
    DECLARE table_cursor CURSOR FOR
        SELECT TABLE_NAME
        FROM information_schema.TABLES
        WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_NAME IN (
                             'sys_config','sys_dept', 'sys_dict_data', 'sys_dict_type',
                             'sys_menu', 'sys_notice', 'sys_post',
                             'sys_role', 'sys_role_dept', 'sys_role_menu',
                             'sys_user_post', 'sys_user_role'
            );

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    -- 创建临时表记录执行日志
    CREATE TEMPORARY TABLE IF NOT EXISTS migration_log (
                                                           table_name VARCHAR(64),
                                                           rows_affected INT,
                                                           status VARCHAR(20),
                                                           error_message TEXT
    );

    OPEN table_cursor;

    table_loop: LOOP
        FETCH table_cursor INTO tbl_name;
        IF done THEN
            LEAVE table_loop;
        END IF;

        -- 检查表是否有shop_id字段
        SELECT COUNT(*) > 0 INTO has_shop_id
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_NAME = tbl_name
          AND COLUMN_NAME = 'shop_id';

        -- 构建字段列表
        SET col_list = (
            SELECT GROUP_CONCAT(
                           CASE
                               WHEN COLUMN_NAME = 'shop_id' THEN CONCAT('\'', new_shop_id, '\'')
                               ELSE COLUMN_NAME
                               END
                           ORDER BY ORDINAL_POSITION
                           SEPARATOR ', '
                   )
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
              AND TABLE_NAME = tbl_name
        );

        -- 动态生成并执行INSERT语句
        SET @sql = CONCAT(
                'INSERT INTO ', tbl_name, ' SELECT ',
                col_list,
                ' FROM `', source_db, '`.', tbl_name,
                IF(has_shop_id, CONCAT(' WHERE shop_id = \'', old_shop_id, '\''), '')
                   );

        -- 执行语句并记录结果
        BEGIN
            DECLARE EXIT HANDLER FOR SQLEXCEPTION
                BEGIN
                    GET DIAGNOSTICS CONDITION 1
                        @err_msg = MESSAGE_TEXT;
                    INSERT INTO migration_log
                    VALUES (tbl_name, 0, 'Failed', @err_msg);
                END;

            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            SET @row_count = ROW_COUNT();
            DEALLOCATE PREPARE stmt;

            INSERT INTO migration_log
            VALUES (tbl_name, @row_count, 'Success', NULL);
        END;
    END LOOP;

    CLOSE table_cursor;

    -- 在迁移完成后插入指定用户记录
    BEGIN
        DECLARE EXIT HANDLER FOR SQLEXCEPTION
            BEGIN
                GET DIAGNOSTICS CONDITION 1
                    @err_msg = MESSAGE_TEXT;
                INSERT INTO migration_log
                VALUES ('sys_user', 0, 'Failed', CONCAT('User insertion failed: ', @err_msg));
            END;

        INSERT INTO `sys_user` (
            `user_id`, `shop_id`, `dept_id`, `user_name`, `nick_name`,
            `user_type`, `email`, `phonenumber`, `sex`, `avatar`,
            `password`, `status`, `del_flag`, `login_ip`, `login_date`,
            `create_by`, `create_time`, `update_by`, `update_time`, `remark`
        ) VALUES (
                     1,
                     new_shop_id,
                     103,
                     user_name,
                     nick_name,
                     '00',
                     '<EMAIL>',
                     phonenumber,
                     '1',
                     '',
                     NULL,
                     '0',
                     '0',
                     '127.0.0.1',
                     '2025-06-30 18:42:04',
                     'admin',
                     '2024-12-31 16:03:18',
                     '',
                     '2025-06-30 18:42:04',
                     '管理员'
                 );

        INSERT INTO migration_log
        VALUES ('sys_user', 1, 'Success', NULL);
    END;

    -- 显示迁移结果
    SELECT * FROM migration_log;

    -- 清理临时表
    DROP TEMPORARY TABLE migration_log;
END